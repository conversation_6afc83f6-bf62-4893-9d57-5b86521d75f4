-- Fix Admin Login Issues
-- Run this SQL in your Supabase SQL Editor to fix authentication

-- Temporarily disable <PERSON><PERSON> for admin_users table to allow login
ALTER TABLE admin_users DISABLE ROW LEVEL SECURITY;

-- Also disable RLS for b2b_users table
ALTER TABLE b2b_users DISABLE ROW LEVEL SECURITY;

-- Verify admin user exists with correct password
SELECT id, name, email, role, status FROM admin_users WHERE email = '<EMAIL>';

-- Verify B2B user exists
SELECT id, name, email, business_name, status FROM b2b_users WHERE email = '<EMAIL>';

-- If admin user doesn't exist, create it
INSERT INTO admin_users (name, email, password, role, status) VALUES 
('Admin User', '<EMAIL>', '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a', 'admin', 'active')
ON CONFLICT (email) DO UPDATE SET 
  password = '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a',
  status = 'active';

-- If B2B user doesn't exist, create it
INSERT INTO b2b_users (name, email, password, business_name, status) VALUES 
('John Smith', '<EMAIL>', '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a', 'Smith Jewelry Store', 'approved')
ON CONFLICT (email) DO UPDATE SET 
  password = '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a',
  status = 'approved';

-- Create proper RLS policies for authentication (optional - can be enabled later)
-- These policies allow the application to authenticate users

-- Policy for admin authentication
CREATE POLICY "Allow admin authentication" ON admin_users
    FOR SELECT USING (true);

-- Policy for B2B authentication  
CREATE POLICY "Allow B2B authentication" ON b2b_users
    FOR SELECT USING (true);

-- Re-enable RLS with proper policies (uncomment if you want to use RLS)
-- ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE b2b_users ENABLE ROW LEVEL SECURITY;

-- Verify the fix worked
SELECT 'Admin user check:' as check_type, COUNT(*) as count FROM admin_users WHERE email = '<EMAIL>' AND status = 'active'
UNION ALL
SELECT 'B2B user check:' as check_type, COUNT(*) as count FROM b2b_users WHERE email = '<EMAIL>' AND status = 'approved';