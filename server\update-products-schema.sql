-- Update Products Table Schema for MRP and Offer Price
-- Run this SQL in your Supabase SQL Editor

-- Add new columns to products table
ALTER TABLE products
ADD COLUMN IF NOT EXISTS mrp DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS offer_price DECIMAL(10,2);

-- Update existing products to have MRP and offer_price based on current price
UPDATE products
SET
  mrp = price * 1.2,  -- Set MRP as 20% higher than current price
  offer_price = price  -- Set offer price as current price
WHERE mrp IS NULL OR offer_price IS NULL;

-- Make MRP and offer_price NOT NULL after setting default values
ALTER TABLE products
ALTER COLUMN mrp SET NOT NULL,
ALTER COLUMN offer_price SET NOT NULL;

-- Add check constraints to ensure MRP >= offer_price
ALTER TABLE products
ADD CONSTRAINT check_mrp_offer_price
CHECK (mrp >= offer_price);

-- Update the formatProduct function in your backend to handle these new fields
-- The backend will now use:
-- - mrp: Maximum Retail Price
-- - offer_price: Selling price (discounted price)
-- - price: Will be set to offer_price for compatibility

-- For B2B pricing, we'll calculate based on percentage discount from offer_price
-- This will be handled in the backend logic, not in the database

-- =====================================================
-- IMAGE STORAGE SETUP FOR SUPABASE
-- =====================================================

-- 1. Create storage bucket for product images (Run this in Supabase Storage)
-- Go to Storage > Create Bucket > Name: "product-images" > Public: true

-- 2. Create storage bucket for category images
-- Go to Storage > Create Bucket > Name: "category-images" > Public: true

-- 3. Set up storage policies (Run these in SQL Editor)

-- Policy for product images bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('product-images', 'product-images', true)
ON CONFLICT (id) DO NOTHING;

-- Policy for category images bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('category-images', 'category-images', true)
ON CONFLICT (id) DO NOTHING;

-- Allow public read access to product images
CREATE POLICY "Public read access for product images" ON storage.objects
FOR SELECT USING (bucket_id = 'product-images');

-- Allow authenticated users to upload product images
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'product-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to update product images
CREATE POLICY "Authenticated users can update product images" ON storage.objects
FOR UPDATE USING (bucket_id = 'product-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to delete product images
CREATE POLICY "Authenticated users can delete product images" ON storage.objects
FOR DELETE USING (bucket_id = 'product-images' AND auth.role() = 'authenticated');

-- Allow public read access to category images
CREATE POLICY "Public read access for category images" ON storage.objects
FOR SELECT USING (bucket_id = 'category-images');

-- Allow authenticated users to upload category images
CREATE POLICY "Authenticated users can upload category images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'category-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to update category images
CREATE POLICY "Authenticated users can update category images" ON storage.objects
FOR UPDATE USING (bucket_id = 'category-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to delete category images
CREATE POLICY "Authenticated users can delete category images" ON storage.objects
FOR DELETE USING (bucket_id = 'category-images' AND auth.role() = 'authenticated');

-- =====================================================
-- UPDATE TABLES FOR IMAGE STORAGE
-- =====================================================

-- Update products table to store image URLs properly
-- The images column should store array of image URLs from Supabase Storage
ALTER TABLE products
ALTER COLUMN images TYPE TEXT[] USING
  CASE
    WHEN images IS NULL THEN ARRAY[]::TEXT[]
    WHEN images = '[]' THEN ARRAY[]::TEXT[]
    ELSE string_to_array(trim(both '"[]' from images::text), ',')
  END;

-- Update categories table to include image column
ALTER TABLE categories
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Add image_url column to categories if it doesn't exist
UPDATE categories SET image_url = NULL WHERE image_url IS NULL;
