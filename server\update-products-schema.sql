-- Update Products Table Schema for MRP and Offer Price
-- Run this SQL in your Supabase SQL Editor

-- Add new columns to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS mrp DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS offer_price DECIMAL(10,2);

-- Update existing products to have MRP and offer_price based on current price
UPDATE products 
SET 
  mrp = price * 1.2,  -- Set MRP as 20% higher than current price
  offer_price = price  -- Set offer price as current price
WHERE mrp IS NULL OR offer_price IS NULL;

-- Make MRP and offer_price NOT NULL after setting default values
ALTER TABLE products 
ALTER COLUMN mrp SET NOT NULL,
ALTER COLUMN offer_price SET NOT NULL;

-- Add check constraints to ensure MRP >= offer_price
ALTER TABLE products 
ADD CONSTRAINT check_mrp_offer_price 
CHECK (mrp >= offer_price);

-- Update the formatProduct function in your backend to handle these new fields
-- The backend will now use:
-- - mrp: Maximum Retail Price
-- - offer_price: Selling price (discounted price)
-- - price: Will be set to offer_price for compatibility

-- For B2B pricing, we'll calculate based on percentage discount from offer_price
-- This will be handled in the backend logic, not in the database
