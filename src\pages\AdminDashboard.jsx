import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  ChartBarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  CogIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../context/AuthContext'
import axios from 'axios'

const AdminDashboard = () => {
  const { user, isAdmin, logout } = useAuth()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)

  // State for different sections
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalOrders: 0,
    pendingApplications: 0,
    totalRevenue: 0
  })
  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState([])
  const [orders, setOrders] = useState([])
  const [applications, setApplications] = useState([])
  const [sellers, setSellers] = useState([])

  // Modal states
  const [showProductModal, setShowProductModal] = useState(false)
  const [showCategoryModal, setShowCategoryModal] = useState(false)
  const [showApplicationModal, setShowApplicationModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)
  const [editingCategory, setEditingCategory] = useState(null)
  const [viewingApplication, setViewingApplication] = useState(null)
  const [productForm, setProductForm] = useState({
    name: '',
    description: '',
    longDescription: '',
    mrp: '',
    offerPrice: '',
    category: '',
    stock: '',
    minQuantity: '1',
    featured: false,
    specifications: {},
    features: [],
    images: []
  })
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    slug: '',
    image: null
  })
  const [settings, setSettings] = useState({
    storeName: 'JW GOLD',
    whatsappNumber: '+1234567890',
    contactEmail: '<EMAIL>',
    b2bDefaultDiscount: '20',
    minimumOrderAmount: '500',
    autoApproveApplications: false
  })

  useEffect(() => {
    if (!user || !isAdmin) {
      navigate('/admin-login')
      return
    }
    fetchDashboardData()
  }, [user, isAdmin, navigate])

  const fetchDashboardData = async () => {
    try {
      // Fetch real data from API - use correct token key
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      // Fetch products
      const productsResponse = await axios.get('http://localhost:5000/api/products', { headers })
      setProducts(productsResponse.data || [])

      // Fetch categories
      const categoriesResponse = await axios.get('http://localhost:5000/api/categories', { headers })
      setCategories(categoriesResponse.data || [])

      // Fetch orders
      const ordersResponse = await axios.get('http://localhost:5000/api/admin/orders', { headers })
      setOrders(ordersResponse.data || [])

      // Fetch applications
      const applicationsResponse = await axios.get('http://localhost:5000/api/admin/applications', { headers })
      setApplications(applicationsResponse.data || [])

      // Fetch B2B sellers
      const sellersResponse = await axios.get('http://localhost:5000/api/admin/sellers', { headers })
      setSellers(sellersResponse.data || [])

      // Fetch settings from Supabase
      try {
        const settingsResponse = await axios.get('http://localhost:5000/api/admin/settings', { headers })
        const settingsData = settingsResponse.data || {}

        // Map database settings to component state
        setSettings({
          storeName: settingsData.store_name || 'JW GOLD',
          whatsappNumber: settingsData.whatsapp_number || '+1234567890',
          contactEmail: settingsData.contact_email || '<EMAIL>',
          b2bDefaultDiscount: settingsData.b2b_default_discount || '20',
          minimumOrderAmount: settingsData.minimum_order_amount || '500',
          autoApproveApplications: settingsData.auto_approve_applications === 'true' || false
        })
      } catch (settingsError) {
        console.error('Error fetching settings:', settingsError)
        // Keep default settings if fetch fails
      }

      // Calculate stats from real data
      const totalProducts = productsResponse.data?.length || 0
      const totalOrders = ordersResponse.data?.length || 0
      const pendingApplications = applicationsResponse.data?.filter(app => app.status === 'pending').length || 0
      const totalRevenue = ordersResponse.data?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0

      setStats({
        totalProducts,
        totalOrders,
        pendingApplications,
        totalRevenue
      })

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      // Show user-friendly error message
      alert('Error loading dashboard data. Please check your connection and try again.')
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(price)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': case 'completed': case 'approved': return 'text-green-600 bg-green-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'inactive': case 'cancelled': case 'rejected': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let password = ''
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  const handleApproveApplication = async (applicationId) => {
    try {
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      // Generate password for the new B2B user
      const password = generatePassword()

      // Approve application and create B2B user account
      await axios.put(`http://localhost:5000/api/admin/applications/${applicationId}/approve`,
        { password },
        { headers }
      )

      // Update local state
      setApplications(prev =>
        prev.map(app =>
          app.id === applicationId
            ? { ...app, status: 'approved' }
            : app
        )
      )

      alert(`Application approved successfully! Generated password: ${password}`)
    } catch (error) {
      console.error('Error approving application:', error)
      alert('Error approving application. Please try again.')
    }
  }

  const handleRejectApplication = async (applicationId) => {
    try {
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      await axios.put(`http://localhost:5000/api/admin/applications/${applicationId}/reject`,
        {},
        { headers }
      )

      setApplications(prev =>
        prev.map(app =>
          app.id === applicationId
            ? { ...app, status: 'rejected' }
            : app
        )
      )
      alert('Application rejected successfully!')
    } catch (error) {
      console.error('Error rejecting application:', error)
      alert('Error rejecting application. Please try again.')
    }
  }

  // Helper functions for product and category management
  const resetProductForm = () => {
    setProductForm({
      name: '',
      description: '',
      longDescription: '',
      mrp: '',
      offerPrice: '',
      category: '',
      stock: '',
      minQuantity: '1',
      featured: false,
      specifications: {},
      features: [],
      images: []
    })
  }

  const handleProductSubmit = async (e) => {
    e.preventDefault()
    try {
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      if (editingProduct) {
        await axios.put(`http://localhost:5000/api/admin/products/${editingProduct.id}`, productForm, { headers })
        alert('Product updated successfully!')
      } else {
        await axios.post('http://localhost:5000/api/admin/products', productForm, { headers })
        alert('Product created successfully!')
      }

      setShowProductModal(false)
      setEditingProduct(null)
      resetProductForm()
      fetchDashboardData()
    } catch (error) {
      console.error('Error saving product:', error)
      alert('Error saving product. Please try again.')
    }
  }

  const handleCategorySubmit = async (e) => {
    e.preventDefault()
    try {
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      if (editingCategory) {
        await axios.put(`http://localhost:5000/api/admin/categories/${editingCategory.id}`, categoryForm, { headers })
        alert('Category updated successfully!')
      } else {
        await axios.post('http://localhost:5000/api/admin/categories', categoryForm, { headers })
        alert('Category created successfully!')
      }

      setShowCategoryModal(false)
      setEditingCategory(null)
      setCategoryForm({name: '', slug: '', image: null})
      fetchDashboardData()
    } catch (error) {
      console.error('Error saving category:', error)
      alert('Error saving category. Please try again.')
    }
  }

  const handleSaveSettings = async () => {
    try {
      const token = localStorage.getItem('jwgold_token')
      const headers = { Authorization: `Bearer ${token}` }

      // Save each setting individually
      const settingsToSave = [
        { key: 'store_name', value: settings.storeName },
        { key: 'whatsapp_number', value: settings.whatsappNumber },
        { key: 'contact_email', value: settings.contactEmail },
        { key: 'b2b_default_discount', value: settings.b2bDefaultDiscount },
        { key: 'minimum_order_amount', value: settings.minimumOrderAmount },
        { key: 'auto_approve_applications', value: settings.autoApproveApplications.toString() }
      ]

      for (const setting of settingsToSave) {
        await axios.put(`http://localhost:5000/api/admin/settings/${setting.key}`,
          { value: setting.value },
          { headers }
        )
      }

      alert('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Error saving settings. Please try again.')
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'products', name: 'Products', icon: ShoppingBagIcon },
    { id: 'categories', name: 'Categories', icon: DocumentTextIcon },
    { id: 'orders', name: 'Orders', icon: DocumentTextIcon },
    { id: 'applications', name: 'B2B Applications', icon: UserGroupIcon },
    { id: 'sellers', name: 'Seller Accounts', icon: UserGroupIcon },
    { id: 'settings', name: 'Settings', icon: CogIcon }
  ]

  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: ShoppingBagIcon,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      title: 'Total Orders',
      value: stats.totalOrders,
      icon: DocumentTextIcon,
      color: 'text-green-600 bg-green-100'
    },
    {
      title: 'Pending Applications',
      value: stats.pendingApplications,
      icon: UserGroupIcon,
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      title: 'Total Revenue',
      value: formatPrice(stats.totalRevenue),
      icon: ChartBarIcon,
      color: 'text-purple-600 bg-purple-100'
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Admin Dashboard
              </h1>
              <p className="text-gray-600">Manage your jewelry store</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.name || 'Admin'}</span>
              <button
                onClick={logout}
                className="btn-secondary text-sm"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-red-500 text-red-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <IconComponent className="h-5 w-5" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {statCards.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${stat.color}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Recent Orders */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Orders</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {orders.slice(0, 5).map((order) => (
                      <div key={order.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Order #{order.id}</p>
                          <p className="text-sm text-gray-600">{order.customer_name || 'Customer'}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{formatPrice(order.total_amount || 0)}</p>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                        </div>
                      </div>
                    ))}
                    {orders.length === 0 && (
                      <p className="text-gray-500 text-center py-4">No orders yet</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Recent Applications */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Applications</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {applications.slice(0, 5).map((app) => (
                      <div key={app.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{app.business_name}</p>
                          <p className="text-sm text-gray-600">{app.contact_person}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(app.status)}`}>
                            {app.status}
                          </span>
                          {app.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApproveApplication(app.id)}
                                className="p-1 text-green-600 hover:bg-green-100 rounded"
                              >
                                <CheckIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleRejectApplication(app.id)}
                                className="p-1 text-red-600 hover:bg-red-100 rounded"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                    {applications.length === 0 && (
                      <p className="text-gray-500 text-center py-4">No applications yet</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'products' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Products Management</h2>
              <button
                onClick={() => setShowProductModal(true)}
                className="btn-primary flex items-center"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Add Product
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRP</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.map((product) => (
                    <tr key={product.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img className="h-10 w-10 rounded-full object-cover" src={product.images?.[0] || '/api/placeholder/40/40'} alt="" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-500">{product.description?.substring(0, 50)}...</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.category}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(product.mrp || product.price)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(product.offerPrice || product.price)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.stock}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(product.status)}`}>
                          {product.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-700">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              setEditingProduct(product)
                              setProductForm({
                                name: product.name,
                                description: product.description,
                                longDescription: product.longDescription || product.description,
                                mrp: product.mrp?.toString() || product.price?.toString() || '',
                                offerPrice: product.offerPrice?.toString() || product.price?.toString() || '',
                                category: product.category,
                                stock: product.stock.toString(),
                                minQuantity: product.minQuantity?.toString() || '1',
                                featured: product.featured || false,
                                specifications: product.specifications || {},
                                features: product.features || [],
                                images: product.images || []
                              })
                              setShowProductModal(true)
                            }}
                            className="text-green-600 hover:text-green-700"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-700">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {products.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No products found. Add your first product!</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'categories' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Categories Management</h2>
              <button
                onClick={() => setShowCategoryModal(true)}
                className="btn-primary flex items-center"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Add Category
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category) => (
                <div key={category.id} className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(category.status)}`}>
                      {category.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    {category.productCount || 0} products
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setEditingCategory(category)
                        setCategoryForm({
                          name: category.name,
                          slug: category.slug
                        })
                        setShowCategoryModal(true)
                      }}
                      className="btn-secondary text-sm"
                    >
                      Edit
                    </button>
                    <button className="btn-danger text-sm">
                      Delete
                    </button>
                  </div>
                </div>
              ))}
              {categories.length === 0 && (
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-500">No categories found. Add your first category!</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Orders Management</h2>

            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{order.id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{order.customer_name || 'Customer'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(order.total_amount || 0)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(order.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-700 mr-2">
                          View
                        </button>
                        <button className="text-green-600 hover:text-green-700">
                          Update
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {orders.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No orders found.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'applications' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">B2B Applications</h2>

            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {applications.map((app) => (
                    <tr key={app.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{app.business_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{app.contact_person}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{app.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(app.status)}`}>
                          {app.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(app.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setViewingApplication(app)
                              setShowApplicationModal(true)
                            }}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            View Details
                          </button>
                          {app.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApproveApplication(app.id)}
                                className="text-green-600 hover:text-green-700"
                              >
                                Approve
                              </button>
                              <button
                                onClick={() => handleRejectApplication(app.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                Reject
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {applications.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No applications found.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'sellers' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Seller Accounts</h2>

            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sellers.map((seller) => (
                    <tr key={seller.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{seller.business_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{seller.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{seller.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{seller.phone}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(seller.status)}`}>
                          {seller.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-700">
                            View Details
                          </button>
                          {seller.status === 'active' && (
                            <button className="text-red-600 hover:text-red-700">
                              Suspend
                            </button>
                          )}
                          {seller.status === 'suspended' && (
                            <button className="text-green-600 hover:text-green-700">
                              Activate
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {sellers.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No seller accounts found.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Settings</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Store Name
                    </label>
                    <input
                      type="text"
                      className="input-field"
                      value={settings.storeName}
                      onChange={(e) => setSettings({...settings, storeName: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WhatsApp Number
                    </label>
                    <input
                      type="text"
                      className="input-field"
                      value={settings.whatsappNumber}
                      onChange={(e) => setSettings({...settings, whatsappNumber: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      className="input-field"
                      value={settings.contactEmail}
                      onChange={(e) => setSettings({...settings, contactEmail: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">B2B Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Default B2B Discount (%)
                    </label>
                    <input
                      type="number"
                      className="input-field"
                      value={settings.b2bDefaultDiscount}
                      onChange={(e) => setSettings({...settings, b2bDefaultDiscount: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Order Amount
                    </label>
                    <input
                      type="number"
                      className="input-field"
                      value={settings.minimumOrderAmount}
                      onChange={(e) => setSettings({...settings, minimumOrderAmount: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="rounded"
                        checked={settings.autoApproveApplications}
                        onChange={(e) => setSettings({...settings, autoApproveApplications: e.target.checked})}
                      />
                      <span className="text-sm text-gray-700">Auto-approve applications</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button onClick={handleSaveSettings} className="btn-primary">
                Save Settings
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Product Modal */}
      {showProductModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              {editingProduct ? 'Edit Product' : 'Create New Product'}
            </h2>
            <form onSubmit={handleProductSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Product Name</label>
                  <input
                    type="text"
                    value={productForm.name}
                    onChange={(e) => setProductForm({...productForm, name: e.target.value})}
                    className="input-field"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={productForm.category}
                    onChange={(e) => setProductForm({...productForm, category: e.target.value})}
                    className="input-field"
                    required
                  >
                    <option value="">Select Category</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.name}>{cat.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Product Images</label>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => {
                    const files = Array.from(e.target.files)
                    setProductForm({...productForm, images: files})
                  }}
                  className="input-field"
                />
                <p className="text-sm text-gray-500 mt-1">Upload multiple images (JPG, PNG)</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={productForm.description}
                  onChange={(e) => setProductForm({...productForm, description: e.target.value})}
                  className="input-field"
                  rows="3"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">MRP (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={productForm.mrp}
                    onChange={(e) => setProductForm({...productForm, mrp: e.target.value})}
                    className="input-field"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Offer Price (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={productForm.offerPrice}
                    onChange={(e) => setProductForm({...productForm, offerPrice: e.target.value})}
                    className="input-field"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Stock</label>
                  <input
                    type="number"
                    value={productForm.stock}
                    onChange={(e) => setProductForm({...productForm, stock: e.target.value})}
                    className="input-field"
                    required
                  />
                </div>
              </div>

              {/* Category-specific specifications */}
              {productForm.category && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Specifications</label>
                  <div className="space-y-3">
                    {productForm.category === 'Rings' && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Metal Type (e.g., Gold, Silver)"
                            value={productForm.specifications.metalType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, metalType: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Stone Type (e.g., Diamond, Ruby)"
                            value={productForm.specifications.stoneType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, stoneType: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Ring Size"
                            value={productForm.specifications.ringSize || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, ringSize: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Weight (grams)"
                            value={productForm.specifications.weight || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, weight: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                      </>
                    )}

                    {productForm.category === 'Necklaces' && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Metal Type"
                            value={productForm.specifications.metalType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, metalType: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Chain Length"
                            value={productForm.specifications.chainLength || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, chainLength: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Weight (grams)"
                            value={productForm.specifications.weight || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, weight: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Clasp Type"
                            value={productForm.specifications.claspType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, claspType: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                      </>
                    )}

                    {productForm.category === 'Earrings' && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Metal Type"
                            value={productForm.specifications.metalType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, metalType: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Stone Type"
                            value={productForm.specifications.stoneType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, stoneType: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <input
                            type="text"
                            placeholder="Earring Type (Stud, Drop, etc.)"
                            value={productForm.specifications.earringType || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, earringType: e.target.value}
                            })}
                            className="input-field"
                          />
                          <input
                            type="text"
                            placeholder="Weight (grams)"
                            value={productForm.specifications.weight || ''}
                            onChange={(e) => setProductForm({
                              ...productForm,
                              specifications: {...productForm.specifications, weight: e.target.value}
                            })}
                            className="input-field"
                          />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={productForm.featured}
                    onChange={(e) => setProductForm({...productForm, featured: e.target.checked})}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-700">Featured Product</span>
                </label>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowProductModal(false)
                    setEditingProduct(null)
                    resetProductForm()
                  }}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button type="submit" className="btn-primary">
                  {editingProduct ? 'Update Product' : 'Create Product'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </h2>
            <form onSubmit={handleCategorySubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm({...categoryForm, name: e.target.value})}
                  className="input-field"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Slug</label>
                <input
                  type="text"
                  value={categoryForm.slug}
                  onChange={(e) => setCategoryForm({...categoryForm, slug: e.target.value})}
                  className="input-field"
                  placeholder="Auto-generated if empty"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category Image</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setCategoryForm({...categoryForm, image: e.target.files[0]})}
                  className="input-field"
                />
                <p className="text-sm text-gray-500 mt-1">Upload category image (JPG, PNG)</p>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowCategoryModal(false)
                    setEditingCategory(null)
                    setCategoryForm({name: '', slug: '', image: null})
                  }}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button type="submit" className="btn-primary">
                  {editingCategory ? 'Update Category' : 'Create Category'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Application Details Modal */}
      {showApplicationModal && viewingApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">B2B Application Details</h2>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Business Name</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.business_name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Contact Person</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.contact_person}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.phone}</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Business Address</label>
                <p className="mt-1 text-sm text-gray-900">{viewingApplication.business_address}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Business Type</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.business_type}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Years in Business</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.years_in_business}</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Previous Experience</label>
                <p className="mt-1 text-sm text-gray-900">{viewingApplication.previous_experience || 'Not provided'}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Website</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.website || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Social Media</label>
                  <p className="mt-1 text-sm text-gray-900">{viewingApplication.social_media || 'Not provided'}</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Additional Information</label>
                <p className="mt-1 text-sm text-gray-900">{viewingApplication.additional_info || 'Not provided'}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(viewingApplication.status)}`}>
                    {viewingApplication.status}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Applied Date</label>
                  <p className="mt-1 text-sm text-gray-900">{new Date(viewingApplication.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={() => {
                  setShowApplicationModal(false)
                  setViewingApplication(null)
                }}
                className="btn-secondary"
              >
                Close
              </button>

              {viewingApplication.status === 'pending' && (
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      handleRejectApplication(viewingApplication.id)
                      setShowApplicationModal(false)
                      setViewingApplication(null)
                    }}
                    className="btn-danger"
                  >
                    Reject
                  </button>
                  <button
                    onClick={() => {
                      handleApproveApplication(viewingApplication.id)
                      setShowApplicationModal(false)
                      setViewingApplication(null)
                    }}
                    className="btn-primary"
                  >
                    Approve & Generate Password
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminDashboard
