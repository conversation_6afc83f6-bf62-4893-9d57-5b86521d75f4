const express = require('express')
const cors = require('cors')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const multer = require('multer')
const path = require('path')
const fs = require('fs')
const { v4: uuidv4 } = require('uuid')
const supabase = require('./supabase')

const app = express()
const PORT = process.env.PORT || 5000
const JWT_SECRET = process.env.JWT_SECRET || 'jwgold_secret_key_2024'

// Middleware
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))
app.use('/uploads', express.static('uploads'))

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads')
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/')
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase())
    const mimetype = allowedTypes.test(file.mimetype)
    
    if (mimetype && extname) {
      return cb(null, true)
    } else {
      cb(new Error('Only image files are allowed'))
    }
  }
})

// Database helper functions
const formatProduct = (product) => {
  return {
    ...product,
    b2bPrice: product.b2b_price,
    minQuantity: product.min_quantity,
    longDescription: product.long_description,
    createdAt: product.created_at
  }
}

const formatUser = (user) => {
  return {
    ...user,
    businessName: user.business_name,
    createdAt: user.created_at
  }
}

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }
    req.user = user
    next()
  })
}

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' })
  }
  next()
}

// B2B middleware
const requireB2B = (req, res, next) => {
  if (req.user.role !== 'b2b') {
    return res.status(403).json({ message: 'B2B access required' })
  }
  next()
}

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'JW GOLD API is running' })
})

// Auth Routes
app.post('/api/admin/login', async (req, res) => {
  try {
    const { email, password } = req.body
    
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .eq('role', 'admin')
      .eq('status', 'active')
      .single()
    
    if (error || !user) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }
    
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }
    
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    )
    
    res.json({
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    })
  } catch (error) {
    console.error('Admin login error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.post('/api/b2b/login', async (req, res) => {
  try {
    const { email, password } = req.body
    
    const { data: user, error } = await supabase
      .from('b2b_users')
      .select('*')
      .eq('email', email)
      .eq('status', 'approved')
      .single()
    
    if (error || !user) {
      return res.status(401).json({ message: 'Invalid credentials or account not approved' })
    }
    
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }
    
    const token = jwt.sign(
      { id: user.id, email: user.email, role: 'b2b' },
      JWT_SECRET,
      { expiresIn: '24h' }
    )
    
    res.json({
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        businessName: user.business_name,
        role: 'b2b'
      }
    })
  } catch (error) {
    console.error('B2B login error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Reseller application
app.post('/api/reseller/apply', async (req, res) => {
  try {
    const applicationData = req.body
    
    // Check if email already exists
    const { data: existingApplication } = await supabase
      .from('reseller_applications')
      .select('id')
      .eq('email', applicationData.email)
      .single()
    
    if (existingApplication) {
      return res.status(400).json({ message: 'Application with this email already exists' })
    }
    
    // Convert camelCase to snake_case for database
    const dbData = {
      first_name: applicationData.firstName,
      last_name: applicationData.lastName,
      email: applicationData.email,
      phone: applicationData.phone,
      business_name: applicationData.businessName,
      business_type: applicationData.businessType,
      business_address: applicationData.businessAddress,
      business_city: applicationData.businessCity,
      business_state: applicationData.businessState,
      business_zip: applicationData.businessZip,
      business_country: applicationData.businessCountry,
      tax_id: applicationData.taxId,
      years_in_business: applicationData.yearsInBusiness,
      target_market: applicationData.targetMarket,
      estimated_monthly_volume: applicationData.estimatedMonthlyVolume,
      previous_experience: applicationData.previousExperience,
      website: applicationData.website,
      social_media: applicationData.socialMedia,
      additional_info: applicationData.additionalInfo,
      status: 'pending'
    }
    
    const { data: newApplication, error } = await supabase
      .from('reseller_applications')
      .insert([dbData])
      .select()
      .single()
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to submit application' })
    }
    
    res.status(201).json({
      message: 'Application submitted successfully',
      applicationId: newApplication.id
    })
  } catch (error) {
    console.error('Application submission error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Product Routes
app.get('/api/products', async (req, res) => {
  try {
    const { category, search, minPrice, maxPrice, featured } = req.query
    
    let query = supabase
      .from('products')
      .select('*')
      .eq('status', 'active')
    
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }
    
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }
    
    if (minPrice) {
      query = query.gte('price', parseFloat(minPrice))
    }
    
    if (maxPrice) {
      query = query.lte('price', parseFloat(maxPrice))
    }
    
    if (featured === 'true') {
      query = query.eq('featured', true)
    }
    
    const { data: products, error } = await query.order('created_at', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch products' })
    }
    
    const formattedProducts = products.map(formatProduct)
    res.json(formattedProducts)
  } catch (error) {
    console.error('Error fetching products:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.get('/api/products/featured', async (req, res) => {
  try {
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .eq('featured', true)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch featured products' })
    }
    
    const formattedProducts = products.map(formatProduct)
    res.json(formattedProducts)
  } catch (error) {
    console.error('Error fetching featured products:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.get('/api/products/b2b', authenticateToken, requireB2B, async (req, res) => {
  try {
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch B2B products' })
    }
    
    const formattedProducts = products.map(formatProduct)
    res.json(formattedProducts)
  } catch (error) {
    console.error('Error fetching B2B products:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.get('/api/products/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id)
    
    const { data: product, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .single()
    
    if (error || !product) {
      return res.status(404).json({ message: 'Product not found' })
    }
    
    const formattedProduct = formatProduct(product)
    res.json(formattedProduct)
  } catch (error) {
    console.error('Error fetching product:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.get('/api/products/related/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id)
    
    // First get the product to find its category
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('category')
      .eq('id', productId)
      .single()
    
    if (productError || !product) {
      return res.status(404).json({ message: 'Product not found' })
    }
    
    // Find related products in the same category
    const { data: relatedProducts, error } = await supabase
      .from('products')
      .select('*')
      .eq('category', product.category)
      .neq('id', productId)
      .eq('status', 'active')
      .limit(4)
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch related products' })
    }
    
    const formattedProducts = relatedProducts.map(formatProduct)
    res.json(formattedProducts)
  } catch (error) {
    console.error('Error fetching related products:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Admin product management
app.post('/api/admin/products', authenticateToken, requireAdmin, upload.array('images', 5), async (req, res) => {
  try {
    const productData = req.body
    const images = req.files ? req.files.map(file => `/uploads/${file.filename}`) : []
    
    // Convert camelCase to snake_case for database
    const dbData = {
      name: productData.name,
      description: productData.description,
      long_description: productData.longDescription || productData.description,
      price: parseFloat(productData.price),
      b2b_price: parseFloat(productData.b2bPrice || productData.price),
      images: JSON.stringify(images),
      category: productData.category,
      stock: parseInt(productData.stock) || 0,
      min_quantity: parseInt(productData.minQuantity) || 1,
      featured: productData.featured || false,
      status: 'active',
      specifications: JSON.stringify(productData.specifications || {}),
      features: JSON.stringify(productData.features || [])
    }
    
    const { data: newProduct, error } = await supabase
      .from('products')
      .insert([dbData])
      .select()
      .single()
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to create product' })
    }
    
    const formattedProduct = formatProduct(newProduct)
    res.status(201).json(formattedProduct)
  } catch (error) {
    console.error('Error creating product:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.put('/api/admin/products/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const productId = parseInt(req.params.id)
    const productData = req.body
    
    // Convert camelCase to snake_case for database
    const dbData = {
      name: productData.name,
      description: productData.description,
      long_description: productData.longDescription,
      price: parseFloat(productData.price),
      b2b_price: parseFloat(productData.b2bPrice),
      category: productData.category,
      stock: parseInt(productData.stock),
      min_quantity: parseInt(productData.minQuantity),
      featured: productData.featured,
      status: productData.status,
      specifications: typeof productData.specifications === 'string' 
        ? productData.specifications 
        : JSON.stringify(productData.specifications || {}),
      features: typeof productData.features === 'string'
        ? productData.features
        : JSON.stringify(productData.features || [])
    }
    
    const { data: updatedProduct, error } = await supabase
      .from('products')
      .update(dbData)
      .eq('id', productId)
      .select()
      .single()
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to update product' })
    }
    
    if (!updatedProduct) {
      return res.status(404).json({ message: 'Product not found' })
    }
    
    const formattedProduct = formatProduct(updatedProduct)
    res.json(formattedProduct)
  } catch (error) {
    console.error('Error updating product:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.delete('/api/admin/products/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const productId = parseInt(req.params.id)
    
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', productId)
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to delete product' })
    }
    
    res.json({ message: 'Product deleted successfully' })
  } catch (error) {
    console.error('Error deleting product:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Category Routes
app.get('/api/categories', async (req, res) => {
  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .eq('status', 'active')
      .order('name')
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch categories' })
    }
    
    res.json(categories)
  } catch (error) {
    console.error('Error fetching categories:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Admin category management
app.post('/api/admin/categories', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name, slug } = req.body
    
    const categoryData = {
      name,
      slug: slug || name.toLowerCase().replace(/\s+/g, '-'),
      status: 'active'
    }
    
    const { data: newCategory, error } = await supabase
      .from('categories')
      .insert([categoryData])
      .select()
      .single()
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to create category' })
    }
    
    res.status(201).json(newCategory)
  } catch (error) {
    console.error('Error creating category:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Order Routes
app.get('/api/orders/user', authenticateToken, async (req, res) => {
  try {
    const { data: orders, error } = await supabase
      .from('orders')
      .select('*')
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch orders' })
    }
    
    res.json(orders)
  } catch (error) {
    console.error('Error fetching user orders:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.get('/api/admin/orders', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { data: orders, error } = await supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch orders' })
    }
    
    res.json(orders)
  } catch (error) {
    console.error('Error fetching orders:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Reseller application management
app.get('/api/admin/applications', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { data: applications, error } = await supabase
      .from('reseller_applications')
      .select('*')
      .order('applied_date', { ascending: false })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch applications' })
    }
    
    // Format for frontend compatibility
    const formattedApplications = applications.map(app => ({
      ...app,
      firstName: app.first_name,
      lastName: app.last_name,
      businessName: app.business_name,
      businessType: app.business_type,
      appliedDate: app.applied_date
    }))
    
    res.json(formattedApplications)
  } catch (error) {
    console.error('Error fetching applications:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.put('/api/admin/applications/:id/approve', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id)
    
    // Get application details
    const { data: application, error: appError } = await supabase
      .from('reseller_applications')
      .select('*')
      .eq('id', applicationId)
      .single()
    
    if (appError || !application) {
      return res.status(404).json({ message: 'Application not found' })
    }
    
    // Update application status
    const { error: updateError } = await supabase
      .from('reseller_applications')
      .update({ 
        status: 'approved',
        reviewed_date: new Date().toISOString(),
        reviewed_by: req.user.id
      })
      .eq('id', applicationId)
    
    if (updateError) {
      console.error('Error updating application:', updateError)
      return res.status(500).json({ message: 'Failed to approve application' })
    }
    
    // Create B2B user account
    const hashedPassword = await bcrypt.hash('password123', 10) // Default password
    const { error: userError } = await supabase
      .from('b2b_users')
      .insert([{
        name: `${application.first_name} ${application.last_name}`,
        email: application.email,
        password: hashedPassword,
        business_name: application.business_name,
        business_type: application.business_type,
        phone: application.phone,
        address: application.business_address,
        city: application.business_city,
        state: application.business_state,
        zip_code: application.business_zip,
        country: application.business_country,
        status: 'approved'
      }])
    
    if (userError) {
      console.error('Error creating B2B user:', userError)
      return res.status(500).json({ message: 'Application approved but failed to create user account' })
    }
    
    res.json({ message: 'Application approved and B2B account created' })
  } catch (error) {
    console.error('Error approving application:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.put('/api/admin/applications/:id/reject', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id)
    
    const { error } = await supabase
      .from('reseller_applications')
      .update({ 
        status: 'rejected',
        reviewed_date: new Date().toISOString(),
        reviewed_by: req.user.id
      })
      .eq('id', applicationId)
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to reject application' })
    }
    
    res.json({ message: 'Application rejected' })
  } catch (error) {
    console.error('Error rejecting application:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Settings management
app.get('/api/admin/settings', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { data: settings, error } = await supabase
      .from('settings')
      .select('*')
      .order('key')
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to fetch settings' })
    }
    
    // Convert to key-value object
    const settingsObj = {}
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value
    })
    
    res.json(settingsObj)
  } catch (error) {
    console.error('Error fetching settings:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

app.put('/api/admin/settings/:key', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { key } = req.params
    const { value } = req.body
    
    const { error } = await supabase
      .from('settings')
      .upsert({ 
        key: key,
        value: value,
        description: `Setting for ${key.replace(/_/g, ' ')}`
      })
    
    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ message: 'Failed to update setting' })
    }
    
    res.json({ message: 'Setting updated successfully' })
  } catch (error) {
    console.error('Error updating setting:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Error:', error)
  res.status(500).json({ message: 'Internal server error' })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 JW GOLD API Server running on port ${PORT}`)
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`)
  console.log(`🔐 Admin login: <EMAIL> / admin123`)
  console.log(`🏢 B2B login: <EMAIL> / password123`)
})

module.exports = app