{"name": "jwgold-jewelry-website", "version": "1.0.0", "description": "Jewelry website with B2B and B2C functionality", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js"}, "dependencies": {"@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.54.0", "axios": "^1.3.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.0", "jspdf": "^2.5.1", "lucide-react": "^0.263.1", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "tailwindcss": "^3.3.0", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "vite": "^4.1.0"}}