# Supabase Database Setup Guide

## 🚀 Quick Setup Instructions

### Step 1: Run the Database Schema

1. **Open your Supabase Dashboard**
   - Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Navigate to your project: `https://qollehvknrteffglhwwk.supabase.co`

2. **Access SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Execute the Schema**
   - Copy the entire content from `server/database-schema.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute the schema

### Step 2: Verify Database Setup

After running the schema, you should see these tables in your database:

- ✅ `categories` - Product categories
- ✅ `products` - All jewelry products
- ✅ `admin_users` - Admin accounts
- ✅ `b2b_users` - B2B customer accounts
- ✅ `reseller_applications` - B2B applications
- ✅ `orders` - Customer orders
- ✅ `order_items` - Order line items
- ✅ `settings` - System settings

### Step 3: Test the Integration

1. **Start the servers** (if not already running):
   ```bash
   # Backend server
   cd server
   node index.js
   
   # Frontend server (in new terminal)
   npm run dev
   ```

2. **Test admin login**:
   - Go to `http://localhost:3000/admin-login`
   - Email: `<EMAIL>`
   - Password: `admin123`

3. **Test B2B login**:
   - Go to `http://localhost:3000/b2b-login`
   - Email: `<EMAIL>`
   - Password: `password123`

## 🔧 Configuration Details

### Environment Variables (Optional)

For production, create a `.env` file in the `server` directory:

```env
SUPABASE_URL=https://qollehvknrteffglhwwk.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.i5CB1_zvndEAlOrtvMoWcCscjs6AW30PK-Qib1a8WUw
JWT_SECRET=jwgold_secret_key_2024
PORT=5000
```

### Database Schema Overview

#### Products Table
- Stores all jewelry items with B2C and B2B pricing
- Supports image galleries, specifications, and features
- Category-based organization
- Stock management and minimum quantities

#### User Management
- **Admin Users**: Full system access
- **B2B Users**: Wholesale customers with special pricing
- **Applications**: Reseller application workflow

#### Order System
- Complete order tracking
- Customer information storage
- WhatsApp integration ready
- Invoice generation support

## 🛠️ Troubleshooting

### Common Issues

1. **"Invalid credentials" error**
   - Ensure the database schema was executed successfully
   - Check that admin_users table has the default admin account

2. **"Failed to fetch products" error**
   - Verify the products table exists and has sample data
   - Check Supabase RLS policies are correctly set

3. **Connection errors**
   - Verify your Supabase URL and API key are correct
   - Check your internet connection

### Reset Database (if needed)

If you need to reset the database:

1. Go to Supabase Dashboard → Settings → Database
2. Click "Reset Database" (⚠️ This will delete all data)
3. Re-run the schema from `database-schema.sql`

## 📊 Sample Data

The schema includes sample data:
- 2 sample products (Diamond Ring, Gold Necklace)
- Default admin user
- Default B2B user for testing
- Product categories
- System settings

## 🔐 Security Features

- Row Level Security (RLS) enabled
- Proper authentication policies
- Password hashing with bcrypt
- JWT token-based authentication
- Admin-only access controls

## 🚀 Production Deployment

For production deployment:

1. **Environment Variables**: Move sensitive data to environment variables
2. **Database Backup**: Set up automated backups in Supabase
3. **SSL**: Ensure HTTPS is enabled
4. **Monitoring**: Set up error tracking and monitoring

---

**✅ Your jewelry website is now connected to Supabase!**

All data will be stored in your cloud database, and the application will scale automatically with Supabase's infrastructure.