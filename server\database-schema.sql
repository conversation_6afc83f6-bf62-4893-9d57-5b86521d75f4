-- JW GOLD Database Schema for Supabase
-- Run this SQL in your Supabase SQL Editor to create all necessary tables

-- Categories Table
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products Table
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    long_description TEXT,
    price DECIMAL(10,2) NOT NULL,
    b2b_price DECIMAL(10,2),
    images JSONB DEFAULT '[]',
    category_id INTEGER REFERENCES categories(id),
    category VARCHAR(100), -- For backward compatibility
    stock INTEGER DEFAULT 0,
    min_quantity INTEGER DEFAULT 1,
    featured BOOL<PERSON><PERSON> DEFAULT false,
    status VARCHAR(20) DEFAULT 'active',
    specifications JSONB DEFAULT '{}',
    features <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin Users Table
CREATE TABLE IF NOT EXISTS admin_users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'admin',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- B2B Users Table
CREATE TABLE IF NOT EXISTS b2b_users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    business_name VARCHAR(255),
    business_type VARCHAR(100),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    status VARCHAR(20) DEFAULT 'pending',
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reseller Applications Table
CREATE TABLE IF NOT EXISTS reseller_applications (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    business_name VARCHAR(255) NOT NULL,
    business_type VARCHAR(100),
    business_address TEXT,
    business_city VARCHAR(100),
    business_state VARCHAR(100),
    business_zip VARCHAR(20),
    business_country VARCHAR(100) DEFAULT 'United States',
    tax_id VARCHAR(100),
    years_in_business INTEGER,
    target_market TEXT,
    estimated_monthly_volume VARCHAR(100),
    previous_experience TEXT,
    website VARCHAR(255),
    social_media VARCHAR(255),
    additional_info TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    applied_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_date TIMESTAMP WITH TIME ZONE,
    reviewed_by INTEGER REFERENCES admin_users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders Table
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    customer_address TEXT,
    customer_city VARCHAR(100),
    customer_state VARCHAR(100),
    customer_zip VARCHAR(20),
    customer_country VARCHAR(100),
    customer_notes TEXT,
    items JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    order_type VARCHAR(20) DEFAULT 'b2c', -- 'b2c' or 'b2b'
    user_id INTEGER, -- For B2B orders
    whatsapp_sent BOOLEAN DEFAULT false,
    invoice_generated BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order Items Table (for detailed tracking)
CREATE TABLE IF NOT EXISTS order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id),
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings Table
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories
INSERT INTO categories (name, slug) VALUES 
('Rings', 'rings'),
('Necklaces', 'necklaces'),
('Earrings', 'earrings'),
('Bracelets', 'bracelets'),
('Pendants', 'pendants'),
('Jewelry Sets', 'sets')
ON CONFLICT (slug) DO NOTHING;

-- Insert default admin user
INSERT INTO admin_users (name, email, password, role) VALUES 
('Admin User', '<EMAIL>', '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Insert default B2B user for testing
INSERT INTO b2b_users (name, email, password, business_name, status) VALUES 
('John Smith', '<EMAIL>', '$2a$10$GTerjTg4Z3Y8yQbGvqGPSu0qeFgmky72YtZwN.h/tYFrUiCop042a', 'Smith Jewelry Store', 'approved')
ON CONFLICT (email) DO NOTHING;

-- Insert sample products
INSERT INTO products (name, description, long_description, price, b2b_price, images, category, stock, min_quantity, featured, specifications, features) VALUES 
(
    'Diamond Solitaire Ring',
    'Elegant diamond solitaire ring with 18k gold band',
    'This exquisite diamond solitaire ring represents the pinnacle of jewelry craftsmanship. Each ring is meticulously handcrafted by our master jewelers using only the finest materials.',
    2500.00,
    2000.00,
    '["/api/placeholder/300/300"]',
    'rings',
    15,
    2,
    true,
    '{"material": "18k White Gold", "gemstone": "Diamond", "caratWeight": "1.0 ct", "clarity": "VS1", "color": "G", "cut": "Round Brilliant"}',
    '["Certified diamond with GIA certificate", "Handcrafted by master jewelers", "18k white gold band", "Lifetime warranty included"]'
),
(
    'Gold Chain Necklace',
    'Classic 18k gold chain necklace, perfect for everyday wear',
    'A timeless piece that complements any outfit. This gold chain necklace is crafted from premium 18k gold.',
    850.00,
    680.00,
    '["/api/placeholder/300/300"]',
    'necklaces',
    25,
    3,
    true,
    '{"material": "18k Yellow Gold", "length": "18 inches", "width": "2mm", "clasp": "Lobster Clasp"}',
    '["18k yellow gold construction", "Durable lobster clasp", "Hypoallergenic", "Free gift box included"]'
)
ON CONFLICT DO NOTHING;

-- Insert default settings
INSERT INTO settings (key, value, description) VALUES 
('store_name', 'JW GOLD', 'Store name'),
('whatsapp_number', '+1234567890', 'WhatsApp number for orders'),
('contact_email', '<EMAIL>', 'Contact email'),
('b2b_default_discount', '20', 'Default B2B discount percentage'),
('minimum_order_amount', '500', 'Minimum order amount for free shipping'),
('auto_approve_applications', 'false', 'Auto approve B2B applications')
ON CONFLICT (key) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(featured);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);
CREATE INDEX IF NOT EXISTS idx_reseller_applications_status ON reseller_applications(status);
CREATE INDEX IF NOT EXISTS idx_b2b_users_status ON b2b_users(status);

-- Enable Row Level Security (RLS)
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE reseller_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE b2b_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Create policies for public access to products and categories
CREATE POLICY "Public can view active products" ON products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Public can view active categories" ON categories
    FOR SELECT USING (status = 'active');

-- Create policies for authenticated users
CREATE POLICY "Authenticated users can insert orders" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Authenticated users can insert order items" ON order_items
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can insert reseller applications" ON reseller_applications
    FOR INSERT WITH CHECK (true);

-- Create policies for admin access (you'll need to implement proper auth)
CREATE POLICY "Admins can do everything on products" ON products
    FOR ALL USING (true);

CREATE POLICY "Admins can do everything on categories" ON categories
    FOR ALL USING (true);

CREATE POLICY "Admins can view all orders" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Admins can view all applications" ON reseller_applications
    FOR ALL USING (true);

CREATE POLICY "Admins can manage B2B users" ON b2b_users
    FOR ALL USING (true);

CREATE POLICY "Admins can manage settings" ON settings
    FOR ALL USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_b2b_users_updated_at BEFORE UPDATE ON b2b_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reseller_applications_updated_at BEFORE UPDATE ON reseller_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();